package com.zy.dam.wx.svc;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.zy.dam.wx.dao.WxUserDAO;
import com.zy.dam.wx.orm.WxUser;
import com.zy.dam.wx.req.form.UserBindForm;
import com.zy.dam.wx.req.form.WxLoginForm;
import com.zy.dam.wx.vo.UserVo;
import com.zy.model.DataResult;
import com.zy.util.CryptUtils;
import com.zy.util.JsonUtils;
import com.zy.util.StringUtils;
import com.zy.util.Utils;
import com.zy.web.WebHelper;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 微信公共服务
 */
@Service
@Slf4j
public class WxSVC {

    private static final String KEY_TOKEN_PREFIX = "WX:";

    @Value("${weixin.appid}")
    private String appid;

    @Value("${weixin.secret}")
    private String secret;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    /**
     * 微信接口令牌
     */
    public String access_token;

    @Resource
    private WxUserDAO userDAO;

    @Resource
    private RedisTemplate<String, String> rt;

    public void setUser(UserVo user) {
        String key = KEY_TOKEN_PREFIX + user.getToken();
        log.info("key: {}", key);
        rt.boundValueOps(key).set(JsonUtils.toString(user));
        rt.expire(key, 2, TimeUnit.DAYS);
        log.info("用户信息写入Redis - 用户ID: {}", user != null ? user.getId() : "null");
    }

    public UserVo getUser(HttpServletRequest request) {
        // 尝试多种方式获取token
        String token = WebHelper.getToken(request);
        String zyToken = request.getHeader("zy-token"); // 小程序发送的token
        String zyTokenOld = request.getHeader("zy_token"); // 兼容旧版本
        String headerToken = request.getHeader("token");

        // 调试：显示所有Token获取方式的结果
        log.info("=== Token获取调试信息 ===");
        log.info("WebHelper.getToken(): {}", token);
        log.info("request.getHeader(\"zy-token\"): {}", zyToken);
        log.info("request.getHeader(\"zy_token\"): {}", zyTokenOld);
        log.info("request.getHeader(\"token\"): {}", headerToken);

        // 调试：显示所有请求头（查找zy_token的实际名称）
        log.info("=== 所有请求头信息 ===");
        java.util.Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            log.info("Header: {} = {}", headerName, headerValue);
        }
        log.info("========================");

        // 微信接口专用
        if (zyToken != null && !zyToken.trim().isEmpty()) {
            token = zyToken;
            log.info("使用zy-token: {}", token);
        } else if (zyTokenOld != null && !zyTokenOld.trim().isEmpty()) {
            token = zyTokenOld;
            log.info("使用zy_token: {}", token);
        } else if (headerToken != null && !headerToken.trim().isEmpty()) {
            token = headerToken;
            log.info("使用header token: {}", token);
        } else if (token != null && !token.trim().isEmpty()) {
            log.info("使用WebHelper token: {}", token);
        } else {
            token = null;
            log.info("所有Token获取方式都为空");
        }
        log.info("最终使用的Token: {}", token);
        log.info("========================");

        UserVo user = null;
        if (token != null) {
            String key = KEY_TOKEN_PREFIX + token;
            log.info("Redis Key: {}", key);

            String value = rt.boundValueOps(key).get();
            log.info("Redis Value: {}", value != null ? "存在" : "不存在");

            if (value != null) {
                user = JsonUtils.toObject(value, UserVo.class);
                log.info("用户信息解析成功 - 用户ID: {}", user != null ? user.getId() : "null");
            }
            rt.expire(key, 2, TimeUnit.DAYS);
        } else {
            log.warn("Token为空，无法获取用户信息");
        }
        return user;
    }

    public DataResult<UserVo> login(WxLoginForm item) {
        if (StringUtils.isNotBlank(item.getUserid())) {
            UserVo user = userDAO.findVo(item.getUserid());
            if (user == null)
                return null;
            if (!user.getId().equals(item.getUserid()))
                return DataResult.err("无效的用户");
            return new DataResult<>(1, user);
        }
        if (StringUtils.isBlank(item.getJscode()))
            return DataResult.err("无效参数");
        String json = null;
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appid + "&secret=" + secret + "&js_code="
                + item.getJscode() + "&grant_type=authorization_code";
        try {
            json = get(url);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        if (json == null || json.length() < 10)
            return DataResult.err("无法获取微信信息");
        String openid = null;
        String unionid = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readValue(json, JsonNode.class);
            JsonNode nodeOpenid = root.get("openid");
            JsonNode nodeUnionid = root.get("unionid");
            if (nodeOpenid != null)
                openid = nodeOpenid.asText();
            if (nodeUnionid != null)
                unionid = nodeUnionid.asText();
        } catch (Exception ignored) {
        }

        WxUser user = null;

        // 判断是否为测试环境
        if ("staging".equals(activeProfile) || "prod".equals(activeProfile)) {
            log.info("测试环境登录，使用固定openid: otcLu5fiM5iePmhAJavFdE-Lg_nY");
            user = userDAO.findByOpenid("otcLu5fiM5iePmhAJavFdE-Lg_nY");
        } else {
            if (openid == null)
                return DataResult.err("获取openid失败");
            log.info("生产环境登录，使用真实openid: {}", openid);
            user = userDAO.findByOpenid(openid);
        }
        log.info("查找到的用户: {}", user != null ? user.getId() : "null");
        // if (unionid == null) throw new GEX("获取unionid失败");
        if (user == null) {
            user = new WxUser();
            user.setUser("0");
            user.setNick(item.getName());
            user.setMaOpenId(openid);
            user.setMaUnionId(unionid);
            user.setAvatar(item.getAvatarUrl());
            user.setLoginIp(item.getLoginIp());
            if (StringUtils.isNotBlank(item.getScene())) {
                // //scene不为空，说明是点击分享链接进来的，要绑定企业关系
                // HbVo hb = gDAO.findHb(item.scene);
                // log.info("进入海报" + hb.user);
            }
            if (unionid != null) {
                // XcxUser oldUser = dao.findOldUser(unionid);
                // if (oldUser != null) {
                // user.gid = oldUser.id;
                // user.tid = oldUser.gid;
                // }
            }
            userDAO.insert(user);
        } else {
            user.setAvatar(item.getAvatarUrl());
            user.setNick(item.getName());
            user.setLoginIp(item.getLoginIp());
            userDAO.updateLogin(user);
        }
        UserVo vo = userDAO.findVo(user.getId());
        if (vo == null)
            new DataResult<>(-1, "保存用户失败");
        return new DataResult<>(1, vo);
    }

    public void logout(UserVo user) {
        if (user != null) {
            String key = KEY_TOKEN_PREFIX + user.getToken();
            rt.delete(key);
        }
    }

    public DataResult<UserVo> bind(UserVo user, UserBindForm form) {
        String id = userDAO.findSysUserId(form.getAccount(), CryptUtils.md5(form.getPassword()));
        if (id == null)
            return DataResult.err("用户密码出错");
        userDAO.bindUser(user.getId(), id);
        UserVo vo = userDAO.findVo(user.getId());
        if (vo != null) {
            vo.setToken(user.getToken());
            setUser(vo);
        }
        return new DataResult<>(1, vo);
    }

    public DataResult<UserVo> unbind(UserVo user) {
        userDAO.bindUser(user.getId(), "0");
        UserVo vo = userDAO.findVo(user.getId());
        if (vo != null) {
            vo.setToken(user.getToken());
            setUser(vo);
        }
        return new DataResult<>(1, vo);
    }

    public void buildAccessToken() {
        // access_token =
        // "32_NZNwkk-N4nUi3O8hASh2pt3YUc8MydyhH0egu8jL7JtdEw20YvEOCWyiesf1mdHag0Uw7TvvLNY6bGHFRClAowgl4Hep1qBcf8Efiz3EaDXQAwc9k_Q6VUXc8a-61LT1ft-_LuUpU3z15Gn2ZFIhABAFLO";
        String json = null;
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid + "&secret="
                + secret;
        try {
            json = get(url);
        } catch (Exception e) {
            log.error("无法获取token", e);
            return;
        }
        if (json == null || json.length() < 10) {
            log.error("无法获取token：" + json);
            return;
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readValue(json, JsonNode.class);
            JsonNode node = root.get("access_token");
            if (node != null)
                access_token = node.asText();
            log.info("微信Token:" + access_token);
        } catch (Exception e) {
            log.error("无法解析token", e);
        }
    }

    public BufferedImage getQrcode(String page, String scene) {
        BufferedImage bi = null;
        // BufferedInputStream bis = null;
        PrintWriter out = null;
        try {
            URL url = new URL("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + access_token);
            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
            httpURLConnection.setRequestMethod("POST");// 提交模式
            // 发送POST请求必须设置如下两行
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(httpURLConnection.getOutputStream());
            // 发送请求参数
            ObjectMapper mapper = new ObjectMapper();
            ObjectNode node = mapper.createObjectNode();
            node.put("scene", scene);
            node.put("page", page);
            node.put("width", 430);
            node.put("auto_color", true);
            node.put("is_hyaline", true);

            out.write(node.toString());
            // flush输出流的缓冲
            out.flush();
            // 开始获取数据
            // bis = new BufferedInputStream(httpURLConnection.getInputStream());
            bi = ImageIO.read(httpURLConnection.getInputStream());
            // ImageIO.write(bi, "JPEG", new File("D:\\a01.jpg"));
            // System.out.println("生成二维码成功");
        } catch (Exception e) {
            log.error("二维码无法生成：https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + access_token, e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                }
            }

        }
        return bi;
    }

    public String saveQrcode(String page, String scene) {
        BufferedImage bi = null;
        // BufferedInputStream bis = null;
        PrintWriter out = null;
        String purl = "";
        try {
            URL url = new URL("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + access_token);
            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
            httpURLConnection.setRequestMethod("POST");// 提交模式
            // 发送POST请求必须设置如下两行
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(httpURLConnection.getOutputStream());
            // 发送请求参数
            ObjectMapper mapper = new ObjectMapper();
            ObjectNode node = mapper.createObjectNode();
            node.put("scene", scene);
            node.put("page", page);
            node.put("width", 430);
            node.put("auto_color", true);
            node.put("is_hyaline", true);

            out.write(node.toString());
            // flush输出流的缓冲
            out.flush();
            // 开始获取数据
            // bis = new BufferedInputStream(httpURLConnection.getInputStream());
            bi = ImageIO.read(httpURLConnection.getInputStream());
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            purl = "/" + sdf.format(date) + "/" + Utils.uuid().replace("-", "") + ".png";
            // ImageIO.write(bi, "JPEG", new File(picUrl + purl));
            // System.out.println("生成二维码成功");
        } catch (Exception e) {
            log.error("二维码无法生成：https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + access_token, e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                }
            }
        }
        return purl;
    }

    /**
     * 将二进制转换成文件保存
     *
     * @param instreams 二进制流
     * @param imgPath   图片的保存路径
     * @param imgName   图片的名称
     * @return 1：保存正常 0：保存失败
     */
    public static int saveToImgByInputStream(InputStream instreams, String imgPath, String imgName) {
        int stateInt = 1;
        if (instreams != null) {
            try {
                File file = new File(imgPath, imgName);// 可以是任何图片格式.jpg,.png等
                FileOutputStream fos = new FileOutputStream(file);
                byte[] b = new byte[1024];
                int nRead = 0;
                while ((nRead = instreams.read(b)) != -1) {
                    fos.write(b, 0, nRead);
                }
                fos.flush();
                fos.close();
            } catch (Exception e) {
                stateInt = 0;
                e.printStackTrace();
            } finally {
            }
        }
        return stateInt;
    }

    /**
     * 发起http请求获取返回结果
     *
     * @param url 服务URL
     * @return JSON字符串
     */
    public String get(String url) {
        InputStream in = null;
        ByteArrayOutputStream bo = null;
        String result = null;
        try {
            HttpURLConnection urlc = (HttpURLConnection) new URL(url).openConnection();
            urlc.setDoOutput(false);
            urlc.setDoInput(true);
            urlc.setUseCaches(false);
            urlc.setRequestMethod("GET");
            urlc.connect();
            // 将返回的输入流转换成字符串
            in = urlc.getInputStream();
            bo = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int l;
            while ((l = in.read(b)) > 0) {
                bo.write(b, 0, l);
            }
            result = bo.toString("UTF-8");
            urlc.disconnect();
        } catch (Exception e) {
            log.info("weixinapp.get方法url====" + url);
            log.info("weixinapp.get方法错误====" + e.getMessage());
            log.error("微信身份授权有误，weixinapp.get方法错误====", e);
            // e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                }
            } else if (bo != null) {
                try {
                    bo.close();
                } catch (IOException e) {
                }
            }
        }
        return result;
    }

}
