package com.zy.init;

import com.zy.core.EC;
import com.zy.core.EX;
import com.zy.model.Result;
import java.io.FileNotFoundException;
import javax.validation.UnexpectedTypeException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

@ResponseBody
@RestControllerAdvice
@RestController
public class ErrorHandler {

    private static final Logger log = LoggerFactory.getLogger(ErrorHandler.class);

    /**
     * 处理空指针的异常
     *
     * @param ex 异常
     * @return 错误结果
     */
    @ResponseBody
    @ExceptionHandler(value = NullPointerException.class)
    public Result exceptionHandler(NullPointerException ex) {
        log.error("空指针异常", ex);
        return new Result(EC.NULL);
    }

    /**
     * 方法参数校验出错
     *
     * @param e 异常
     * @return Result 错误结果
     */
    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Result exceptionHandler(MethodArgumentNotValidException e) {
        StringBuilder err = new StringBuilder();
        if (e.hasErrors()) {
            for (ObjectError oe : e.getAllErrors()) {
                if (err.length() > 0) err.append("\r\n");
                err.append(oe.getDefaultMessage());
            }
        }
        return new Result(EC.ARG_NOT_VALID, err.length() > 0 ? err.toString() : e.getMessage());
    }

    /**
     * 类型校验出错
     *
     * @param ex 异常
     * @return Result 错误结果
     */
    @ResponseBody
    @ExceptionHandler({UnexpectedTypeException.class})
    public Result handleTypeValidate(UnexpectedTypeException ex) {
        log.error("无法找到校验器", ex);
        return new Result(EC.ARG_NOT_VALID, "无法找到校验器：" + ex.getMessage());
    }

    /**
     * HTTP方法不支持
     *
     * @param ex 异常
     * @return Result
     */
    @ResponseBody
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Result handleMethodNotSupported(HttpRequestMethodNotSupportedException ex) {
        log.error("HTTP方法不支持: {}, 支持的方法: {}", ex.getMethod(), ex.getSupportedMethods());
        return new Result(EC.ARG_NOT_VALID, "请求方法不支持，当前方法: " + ex.getMethod() +
                ", 支持的方法: " + String.join(", ", ex.getSupportedMethods()));
    }

    /**
     * 页面不存在
     *
     * @return Result
     */
    @ResponseBody
    @ExceptionHandler({FileNotFoundException.class, NoHandlerFoundException.class})
    public Result handleNotFound() {
        return new Result(EC.NOT_FOUND);
    }

    /**
     * 处理其他异常
     *
     * @param ex 异常
     * @return 错误结果
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public Result exceptionHandler(Exception ex) {
        if (ex instanceof EX) {
            EX e = (EX) ex;
            Result r = new Result(e.getCode(), ex.getMessage());
            r.setData(((EX) ex).getData());
            return r;
        }
        log.error("系统内部异常", ex);
        return new Result(EC.ERR_SERVER, ex.getMessage());
    }


}
