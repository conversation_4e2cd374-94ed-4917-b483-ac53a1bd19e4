zy:
  name: "基于物联网彩票设备管理平台"
  author: "<PERSON><PERSON>"
  url: "http://localhost"
  timeout: 120 #session超时（分钟）
  job: true # 任务用标志
  sms:
    domain: "dysmsapi.aliyuncs.com"
    accessKeyId: "LTAI5tLVwFVnB9shckJQsxGK"
    accessKeySecret: "******************************"
    woSign: "海南体彩设备"
    woCode: "SMS_465398003" # 工单模板编号，工单号：20041603389，参数：code
  no:               #编码规则类型定义
    asset: ASSET    #资产

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${SPRING_DATASOURCE_URL:**********************************************************************************************************************************************}
    username: ${SPRING_DATASOURCE_USERNAME:ticai}
    password: ${SPRING_DATASOURCE_PASSWORD:ticai123}
    hikari:
      connection-test-query: select now()  #检测数据库的查询语句
      test-on-borrow: true
      min-evictable-idle-time-millis: 600000 #每隔五分钟检测空闲超过10分钟的连接
      time-between-eviction-runs-millis: 300000
      maximum-pool-size: 50 #池中最大连接数（包括空闲和正在使用的连接）默认值是10
      minimum-idle: 10 #池中最小空闲连接数量。默认值10
      pool-name: zy-ds-pool #连接池的名字。
      auto-commit: true #是否自动提交池中返回的连接。默认值为true
      idle-timeout: 10000 #空闲时间，毫秒
      max-lifetime: 500000 #连接池中连接的最大生命周期。
      connection-timeout: 20000 #连接超时时间，毫秒。默认值为30s

  servlet:
    session:
      cookie:
        secure: false  # Docker环境下使用HTTP
        http-only: true

  redis:
    database: 0 #数据库索引（默认为0）
    host: ${SPRING_REDIS_HOST:redis}
    port: ${SPRING_REDIS_PORT:6379}
    password: ${SPRING_REDIS_PASSWORD:}
    pool:
      max-active: 500 #连接池最大连接数（使用负值表示没有限制）
      max-wait: -1 #连接池最大阻塞等待时间（使用负值表示没有限制）
      max-idle: 20 #连接池中的最大空闲连接
      min-idle: 10 #连接池中的最小空闲连接
      timeout: 5000 #连接超时时间（毫秒）

file:
  path: /data/attach
  context: /attach

logging:
  level:
    org.springframework.boot.autoconfigure: INFO
    com.zy: DEBUG
  file:
    name: /app/logs/dam-port.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

weixin:
  appid: "wx6656f02e1b896e38"
  secret: "3a82f3ba9391256a7171a3dba2e20929"

amap:
  key: "b4ae4625e7f217801a29af656cbf8ce7"
